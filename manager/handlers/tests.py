import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from manager.states.states_tests import ManagerTestsStatisticsStates, STATE_TRANSITIONS, STATE_HANDLERS
from common.tests_statistics.register_handlers import register_test_statistics_handlers
from common.tests_statistics.menu import show_tests_statistics_menu
from manager.keyboards.analytics import get_staff_type_selection_kb, get_staff_kb
from common.utils import check_if_id_in_callback_data

# Настраиваем логгер
logger = logging.getLogger(__name__)

router = Router()

# Регистрируем базовые обработчики для менеджера
register_test_statistics_handlers(router, ManagerTestsStatisticsStates, "manager")

# Дополнительные обработчики для выбора персонала

# Обработчики для входного теста курса
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_course_entry_test")
async def manager_select_staff_type_for_course_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для входного теста курса"""
    logger.info("Вызван обработчик manager_select_staff_type_for_course_entry")
    await manager_select_staff_type_for_course_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_course_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_course_entry, F.data.startswith("staff_type_"))
async def manager_select_staff_for_course_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для входного теста курса"""
    logger.info("Вызван обработчик manager_select_staff_for_course_entry")
    await manager_select_staff_for_course_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_course_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_course_entry, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_course_entry_show_subjects_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору предметов после выбора сотрудника"""
    logger.info("Вызван обработчик manager_course_entry_show_subjects")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору предметов (используем существующий обработчик)
    from common.tests_statistics.handlers import show_course_entry_groups
    await show_course_entry_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_select_subject)

# Обработчики для входного теста месяца
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_month_entry_test")
async def manager_select_staff_type_for_month_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для входного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_type_for_month_entry")
    await manager_select_staff_type_for_month_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_month_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_month_entry, F.data.startswith("staff_type_"))
async def manager_select_staff_for_month_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для входного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_for_month_entry")
    await manager_select_staff_for_month_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_month_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_month_entry, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_month_entry_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_month_entry_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_month_entry_groups
    await show_month_entry_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_select_group)

# Обработчики для контрольного теста месяца
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_month_control_test")
async def manager_select_staff_type_for_month_control_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для контрольного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_type_for_month_control")
    await manager_select_staff_type_for_month_control(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_month_control)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_month_control, F.data.startswith("staff_type_"))
async def manager_select_staff_for_month_control_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для контрольного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_for_month_control")
    await manager_select_staff_for_month_control(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_month_control)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_month_control, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_month_control_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_month_control_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_month_control_groups
    await show_month_control_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_select_group)

# Обработчики для пробного ЕНТ
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_ent_test")
async def manager_select_staff_type_for_ent_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для пробного ЕНТ"""
    logger.info("Вызван обработчик manager_select_staff_type_for_ent")
    await manager_select_staff_type_for_ent(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_ent)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_ent, F.data.startswith("staff_type_"))
async def manager_select_staff_for_ent_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для пробного ЕНТ"""
    logger.info("Вызван обработчик manager_select_staff_for_ent")
    await manager_select_staff_for_ent(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_ent)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_ent, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_ent_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_ent_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_ent_groups
    await show_ent_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.ent_select_group)

# Функции-обработчики для состояний (используются в STATE_HANDLERS)
async def manager_select_staff_type_for_course_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для входного теста курса"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики входного теста курса:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_month_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для входного теста месяца"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики входного теста месяца:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_month_control(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для контрольного теста месяца"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики контрольного теста месяца:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_ent(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для пробного ЕНТ"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики пробного ЕНТ:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_for_course_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для входного теста курса"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики входного теста курса:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_month_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для входного теста месяца"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики входного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_month_control(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для контрольного теста месяца"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики контрольного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_ent(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для пробного ЕНТ"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики пробного ЕНТ:",
        reply_markup=staff_kb
    )
